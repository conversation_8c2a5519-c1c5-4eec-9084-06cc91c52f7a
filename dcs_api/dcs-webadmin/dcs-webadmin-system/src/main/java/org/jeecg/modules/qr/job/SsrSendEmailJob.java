package org.jeecg.modules.qr.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;

//import org.jeecg.modules.qr.ssrEmailSendTask.common.EmailSend;
//import org.jeecg.modules.qr.ssrEmailSendTask.entity.SsrEmailSendTask;
//import org.jeecg.modules.qr.ssrEmailSendTask.enums.SendEmailStatusEnum;
//import org.jeecg.modules.qr.ssrEmailSendTask.service.ISsrEmailSendTaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * @Description: 邮件发送定时任务执行类
 * @Author: xie
 * @Date:   2020-04-14
 * @Version: V1.0
 */
@Slf4j
@Component
public class SsrSendEmailJob {
//    @Autowired
//    ISsrEmailSendTaskService ssrEmailSendTaskService;
//    @Autowired
//    EmailSend emailSend;
//
//    @XxlJob("demoJobHandler")
//    public ReturnT<String> execute(String param)  {
//        System.out.println("邮件发送Job， " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//        // 1.读取邮件发送任务列表，只查询未发送的
//        QueryWrapper<SsrEmailSendTask> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("status", SendEmailStatusEnum.WAIT.getCode());
//        //获取邮件发送任务列表
//        List<SsrEmailSendTask> ssrEmailSendTaskList = ssrEmailSendTaskService.list(queryWrapper);
//        //循环任务，发送邮件
//        for (SsrEmailSendTask ssrEmailSendTask : ssrEmailSendTaskList) {
//
//            try {
//
//                boolean result = emailSend.sendEmail(ssrEmailSendTask.getTitle(), ssrEmailSendTask.getContent(), ssrEmailSendTask.getMail());
//                if (result) {
//                    // 发送邮件成功
//                    ssrEmailSendTask.setStatus(SendEmailStatusEnum.SUCCESS.getCode());
//                } else {
//                    // 发送邮件失败
//                    ssrEmailSendTask.setStatus(SendEmailStatusEnum.FAIL.getCode());
//                }
//            } catch (Exception e) {
//                // 发送邮件出现异常
//                ssrEmailSendTask.setStatus(SendEmailStatusEnum.FAIL.getCode());
//            }
//            ssrEmailSendTask.setUpdateTime(new Date());
//            // 发送结果回写到数据库
//            ssrEmailSendTaskService.updateById(ssrEmailSendTask);
//        }
//        return ReturnT.SUCCESS;
//    }
}
