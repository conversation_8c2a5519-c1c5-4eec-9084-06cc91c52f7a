<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.qr.ssrVm.mapper.SsrVmMapper">
    <select id="selectListAll" resultType="org.jeecg.modules.qr.ssrVm.entity.SsrVm">
        select a.*
        from ssr_vm as a
        <where>
            <if test="ssrVm.code != null">
                and a.snid code #{ssrVm.code}
            </if>

            <if test="ssrVm.snid != null">
                and a.snid like #{ssrVm.snid}
            </if>

<!--            <if test="ssrVm.deviceUuid != null">-->
<!--                and a.device_uuid >= #{ssrVm.createTime_begin}-->
<!--            </if>-->

            <if test="ssrVm.no != null">
                and a.address = #{ssrVm.no}
            </if>

            <if test="sysOrgCode != null">
                and a.sys_org_code like #{sysOrgCode}"%"
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>