package org.jeecg.modules.qr.home;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.dcs.device.entity.Sensors;
import org.jeecg.modules.dcs.device.service.ISensorsService;
import org.jeecg.modules.qr.ssrSysUserClient.entity.SsrSysUserClient;
import org.jeecg.modules.qr.ssrSysUserClient.service.ISsrSysUserClientService;
import org.jeecg.modules.qr.ssrVm.entity.SsrVm;
import org.jeecg.modules.qr.ssrVm.service.ISsrVmService;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.service.ISysRoleService;
import org.jeecg.modules.tmr.tmrChickenfarm.entity.TmrChickenfarm;
import org.jeecg.modules.tmr.tmrChickenfarm.service.ITmrChickenfarmService;
import org.jeecg.modules.tmr.tmrCompany.entity.TmrCompany;
import org.jeecg.modules.tmr.tmrCompany.service.ITmrCompanyService;
import org.jeecg.modules.tmr.tmrHenhouse.entity.TmrHenhouse;
import org.jeecg.modules.tmr.tmrHenhouse.service.ITmrHenhouseService;
import org.jeecg.modules.tmr.tmrRestaurant.service.ITmrRestaurantService;
import org.jeecg.modules.tmr.tmrSensor.entity.TmrSensor;
import org.jeecg.modules.tmr.tmrSensor.entity.TmrSensorJs;
import org.jeecg.modules.tmr.tmrSensor.service.ITmrSensorJsService;
import org.jeecg.modules.tmr.tmrSensor.service.ITmrSensorService;
import org.jeecg.modules.tmr.tmrTables.entity.TmrTables;
import org.jeecg.modules.tmr.tmrTables.service.ITmrTablesService;
import org.jeecg.modules.dcs.common.util.UserCompanyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/home/<USER>")
@Slf4j
//@RequiresRoles(value = { "admin","ssradmin","clientadmin","clientuser"}, logical = Logical.OR)
public class HomePageController {
    @Autowired
    private ITmrCompanyService tmrCompanyService;
    @Autowired
    private ITmrChickenfarmService tmrChickenfarmService;
    @Autowired
    private ITmrHenhouseService tmrHenhouseService;
    @Autowired
    private ITmrSensorJsService tmrSensorJsService;
    @Resource
    private ISensorsService  sensorsService;
    @Autowired
    private UserCompanyUtil userCompanyUtil;



    /**
     /**     * 首页数据统计
     * @param req
     * @return
     */
    @GetMapping(value = "/homeList")
    public Result<?> homeList(HttpServletRequest req){
        Map<String,Integer> homeList = new HashMap<>();

        // 使用工具类获取当前用户信息和权限
        String filterCompanyId = userCompanyUtil.getFilterCompanyId();
        boolean isAdmin = userCompanyUtil.isCurrentUserAdmin();

        if (userCompanyUtil.needCompanyFilter()) {
            // 普通用户，按公司过滤数据
            //公司数量 - 普通用户只能看到自己的公司
            homeList.put("companyCount", 1);

            //鸡厂数量 - 按公司ID过滤
//            LambdaQueryWrapper<TmrChickenfarm> chickenWrapper = new LambdaQueryWrapper<>();
//            chickenWrapper.eq(TmrChickenfarm::getCompanyId, filterCompanyId);
//            int chickenCount = tmrChickenfarmService.count(chickenWrapper);
//            homeList.put("chickenCount", chickenCount);

            //鸡舍总数量 - 按公司ID过滤
//            LambdaQueryWrapper<TmrHenhouse> henhouseWrapper = new LambdaQueryWrapper<>();
//            henhouseWrapper.eq(TmrHenhouse::getCompanyId, filterCompanyId);
//            int henhouseCount = tmrHenhouseService.count(henhouseWrapper);
//            homeList.put("henhouseCount", henhouseCount);

            //传感器的数量 - 通过关联点位表按公司ID过滤
            QueryWrapper<Sensors> sensorWrapper = new QueryWrapper<>();
            sensorWrapper.inSql("location_id",
                "SELECT location_id FROM locations WHERE company_id = '" + filterCompanyId + "'");
            int sensorCount = sensorsService.count(sensorWrapper);
            homeList.put("sensorCount", sensorCount);
        } else {
            // 管理员用户，显示全部数据
            //公司数量
            int companyCount = tmrCompanyService.count();
            homeList.put("companyCount", companyCount);

            //鸡厂数量
            int chickenCount = tmrChickenfarmService.count();
            homeList.put("chickenCount", chickenCount);

            //鸡舍总数量
            int henhouseCount = tmrHenhouseService.count();
            homeList.put("henhouseCount", henhouseCount);

            //传感器的数量
            int sensorCount = sensorsService.count();
            homeList.put("sensorCount", sensorCount);
        }

        return Result.ok(homeList);
    }
}
