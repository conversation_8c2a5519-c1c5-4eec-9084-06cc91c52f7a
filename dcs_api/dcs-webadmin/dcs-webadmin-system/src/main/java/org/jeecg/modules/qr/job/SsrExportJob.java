package org.jeecg.modules.qr.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.qr.amazonOss.service.AmazonService;
import org.jeecg.modules.qr.ssrexportdownloadcenter.entity.SsrExportDownloadCenter;
import org.jeecg.modules.qr.ssrexportdownloadcenter.service.ISsrExportDownloadCenterService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Description: 导出任务定时任务执行类
 * @Author: jeecg-boot
 * @Date:   2020-04-26
 * @Version: V1.0
 */
//持久化
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
@Slf4j
public class SsrExportJob extends QuartzJobBean{
    @Autowired
    ISsrExportDownloadCenterService ssrExportDownloadCenterService;


    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        try {
            //通用查询条件，判断导出类型
            QueryWrapper<SsrExportDownloadCenter> queryWrapper = new QueryWrapper<SsrExportDownloadCenter>();
            queryWrapper.eq("export_result", "UNDO");
            //获取导出任务列表
            List<SsrExportDownloadCenter> exportList = ssrExportDownloadCenterService.list(queryWrapper);
            //循环任务，导出文件
            for (SsrExportDownloadCenter onexport : exportList) {

                try {

                    if (ssrExportDownloadCenterService.export(onexport)) {

                        onexport.setExportFinishTime(new Date());
                        onexport.setExportResult("OK");
                    } else {
                        onexport.setExportResult("ERROR");
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    log.error(e.getMessage(), e);
                    onexport.setExportResult("ERROR");
                }
                onexport.setUpdateTime(new Date());
                // 发送结果回写到数据库
                ssrExportDownloadCenterService.updateById(onexport);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }

    }
}
