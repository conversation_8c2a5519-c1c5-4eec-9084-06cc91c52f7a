package org.jeecg.config;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.RealmSecurityManager;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.jeecg.modules.shiro.authc.ShiroRealm;

public class ShiroUtils {
    /**
     * @param principal
     * @title 刷新用户权限 重新授权
     * @desc principal为用户的认证信息
     */
    public static void reloadAuthorizing(Object principal) throws Exception {
        RealmSecurityManager rsm = (RealmSecurityManager) SecurityUtils.getSecurityManager();
        ShiroRealm myShiroRealm = (ShiroRealm) rsm.getRealms().iterator().next();
        Subject subject = SecurityUtils.getSubject();
        if (subject != null) {
            String realmName = subject.getPrincipals().getRealmNames().iterator().next();
            SimplePrincipalCollection principals = new SimplePrincipalCollection(principal, realmName);
            subject.runAs(principals);
            if (myShiroRealm.isAuthenticationCachingEnabled()) {
                myShiroRealm.getAuthenticationCache().remove(principals);
            }
            if (myShiroRealm.isAuthorizationCachingEnabled()) {
                // 删除指定用户shiro权限
                myShiroRealm.getAuthorizationCache().remove(principals);
            }
            // 刷新权限
            subject.releaseRunAs();
        }

    }
}
