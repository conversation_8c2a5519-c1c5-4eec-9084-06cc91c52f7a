package org.jeecg.modules.qr.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.qr.ssrQrOrder.service.ISsrQrOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Description: 超时订单退款定时任务执行类
 * @Author: swangwei01
 * @Date:   2020-04-23
 * @Version: V1.0
 */
@Slf4j
@Component
public class TimeOutOrderRefund {
    @Autowired
    ISsrQrOrderService ssrQrOrderService;

//    @XxlJob("refundJobHandler")
//    public ReturnT<String> execute(String param)  {
//        try {
//            ssrQrOrderService.timeOutOrderRefund();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return ReturnT.SUCCESS;
//    }
}
