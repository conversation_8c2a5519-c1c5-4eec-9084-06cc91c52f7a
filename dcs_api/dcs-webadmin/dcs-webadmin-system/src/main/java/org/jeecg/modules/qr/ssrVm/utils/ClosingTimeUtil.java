package org.jeecg.modules.qr.ssrVm.utils;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 仮締め時刻の自動生成方法
 * <AUTHOR>
 */
@Component
public class ClosingTimeUtil {

    public static final int minHour = 2;
    public static final int maxHour = 5;

    public static final int maxLoop = 5;
    /**
     * 时间分割 3600，1800，900 ...
     */
    public static final int[]  timeDivision = {3600,1800,900,450,225,75,25,5,1,0};
    /**
     * 时间快速查重map
     */
    public static final Map<Integer, byte[] > timeMap = new  HashMap();

//    @PostConstruct
    private void init()   {
        byte[]  map3600 = new byte[3600];
        Arrays.fill(map3600, (byte) 0);
        byte[] last = null;

        timeMap.put(timeDivision[0],map3600);
//        map3600 = initMap(timeDivision[0],map3600);

        for (int i=0; i<timeDivision.length; i++){
//            if(0 == i){
//                timeMap.put(timeDivision[0],map3600);
//                map3600 = initMap(timeDivision[0],map3600);
//            }

            if(timeDivision[i] > 0){
                byte[] dataRet = initMap(timeDivision[i],map3600);
                map3600 = dataRet;
                timeMap.put(timeDivision[i+1],dataRet);
            }

        }
        System.out.println("ClosingTimeUtil init finish");
    }

    private static byte[] initMap(Integer timeDivision,byte[] preMap){
        byte[] newMap = Arrays.copyOf(preMap,3600);
        if(3600 == timeDivision){
            newMap[0] = 1;
        }else{
            for (Integer index = timeDivision ; index<3600; index+=timeDivision){

                newMap[index] = 1;
            }
        }
        return newMap;
    }

}
