package org.jeecg.modules.qr.redis;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: redis配置
 * @Author: swangwei01
 * @Date:   2020-04-27
 * @Version: V1.0
 */
@Configuration
public class RedissonConfig {
    //redis端口
    @Value("${spring.redis.port}")
    private String port;

    //redis地址
    @Value("${spring.redis.host}")
    private String host;

    //redis密码
    @Value("${spring.redis.password}")
    private String redisPassword;

    @Bean
    public RedissonClient redissonClient(){
        Config config = new Config();
        config.useSingleServer().setAddress("redis://" + host + ":" + port).setPassword(redisPassword);
        //创建redissonClient
        RedissonClient redissonClient = Redisson.create(config);
        return redissonClient;
    }
}
