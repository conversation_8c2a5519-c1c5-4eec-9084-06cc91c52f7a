package org.jeecg.modules.qr.job;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.qr.amazonOss.service.AmazonService;
import org.jeecg.modules.qr.ssrexportdownloadcenter.entity.SsrExportDownloadCenter;
import org.jeecg.modules.qr.ssrexportdownloadcenter.service.ISsrExportDownloadCenterService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.Date;
import java.util.List;

/**
 * @Description: 导出任务定时任务执行类
 * @Author: jeecg-boot
 * @Date:   2020-04-26
 * @Version: V1.0
 */
//持久化
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
@Slf4j
public class SsrDeleteOldFileJob extends QuartzJobBean{
    @Autowired
    ISsrExportDownloadCenterService ssrExportDownloadCenterService;
    @Autowired
    AmazonService amazonService;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        try {
            //通用查询条件，判断导出类型
            QueryWrapper<SsrExportDownloadCenter> queryWrapper = new QueryWrapper<SsrExportDownloadCenter>();


            Date date = DateUtil.date();
            Date datebegin = DateUtil.beginOfDay(date);
            Date nedatePreMonth = DateUtil.offset(datebegin, DateField.MONTH, -1);
//            DateUtil.endOfDay(nedatePreMonth);
            queryWrapper.le("create_time",nedatePreMonth);

            //获取导出任务列表
            List<SsrExportDownloadCenter> exportList = ssrExportDownloadCenterService.list(queryWrapper);
            //循环任务，导出文件
            for (SsrExportDownloadCenter onexport : exportList) {
                try {
                    if(!StringUtils.isEmpty(onexport.getDownloadFile())){
                        amazonService.delete(onexport.getDownloadFile());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error(e.getMessage(), e);
                }

                ssrExportDownloadCenterService.removeById(onexport.getId());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }

    }
}
