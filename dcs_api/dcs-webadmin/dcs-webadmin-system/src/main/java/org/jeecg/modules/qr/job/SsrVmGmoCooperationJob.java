package org.jeecg.modules.qr.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.util.ExportUtil;
import org.jeecg.modules.qr.qrTerminalCollectionHistory.entity.QrTerminalCollectionHistory;
import org.jeecg.modules.qr.qrTerminalCollectionHistory.service.IQrTerminalCollectionHistoryService;
import org.jeecg.modules.qr.sftp.SftpConfig;
import org.jeecg.modules.qr.ssrClient.entity.SsrClient;
import org.jeecg.modules.qr.ssrClient.service.ISsrClientService;
import org.jeecg.modules.qr.ssrVm.entity.SsrVm;
import org.jeecg.modules.qr.ssrVm.service.ISsrVmService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

//持久化
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
@Slf4j
public class SsrVmGmoCooperationJob extends QuartzJobBean {
    @Autowired
    ISsrClientService ssrClientService;

    @Autowired
    ISsrVmService ssrVmService;

    @Autowired
    IQrTerminalCollectionHistoryService qrTerminalCollectionHistoryService;

    @Autowired
    SftpConfig.SftpGateway sftpGateway;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try {
            QueryWrapper<SsrClient> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_cal", 1);
            List<SsrClient> ssrClientList = ssrClientService.list(queryWrapper);

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            //获取当前月第一天时间
            cal.set(Calendar.DAY_OF_MONTH, 1);
            Date firstDayOfThisMonth = cal.getTime();
            //获取上月第一天时间
            cal.add(Calendar.MONTH, -1);
            Date firstDayOfLastMonth = cal.getTime();

            List<Map<String, Object>> dataList =  new ArrayList<>();
            for (SsrClient ssrClient : ssrClientList) {
                QueryWrapper<SsrVm> vmQueryWrapper = new QueryWrapper<>();
                vmQueryWrapper.eq("client_id", ssrClient.getId());
                vmQueryWrapper.and(i -> i.isNotNull("shop_code").or().isNotNull("tid"));
                List<SsrVm> ssrVmList = ssrVmService.list(vmQueryWrapper);
                for (SsrVm ssrVm : ssrVmList) {
                    if (StringUtils.isNotBlank(ssrVm.getTid()) || StringUtils.isNotBlank(ssrVm.getShopCode())) {
                        //获取上上月orderResult为5的端末集信履历数据，按时间倒序
                        QueryWrapper<QrTerminalCollectionHistory> queryWrapperOfTheMonthBeforeLast = new QueryWrapper<>();
                        queryWrapperOfTheMonthBeforeLast.eq("type", 5);
                        queryWrapperOfTheMonthBeforeLast.eq("snid", ssrVm.getSnid());
                        queryWrapperOfTheMonthBeforeLast.lt("collection_time", firstDayOfLastMonth);
                        queryWrapperOfTheMonthBeforeLast.orderByDesc("collection_time");
                        queryWrapperOfTheMonthBeforeLast.last("limit 1");
                        List<QrTerminalCollectionHistory> dataOfTheMonthBeforeLast = qrTerminalCollectionHistoryService.list(queryWrapperOfTheMonthBeforeLast);

                        //获取上月orderResult为5的端末集信履历数据，按时间倒序
                        QueryWrapper<QrTerminalCollectionHistory> queryWrapperOfLastMonth = new QueryWrapper<>();
                        queryWrapperOfLastMonth.eq("type", 5);
                        queryWrapperOfLastMonth.eq("snid", ssrVm.getSnid());
                        queryWrapperOfLastMonth.lt("collection_time", firstDayOfThisMonth);
                        queryWrapperOfLastMonth.ge("collection_time", firstDayOfLastMonth);
                        queryWrapperOfLastMonth.orderByDesc("collection_time");
                        queryWrapperOfLastMonth.last("limit 1");
                        List<QrTerminalCollectionHistory> dataOfLastMonth = qrTerminalCollectionHistoryService.list(queryWrapperOfLastMonth);

                        //上个月有端末集信履历数据时才写入数据
                        if (dataOfLastMonth.size() > 0) {
                            String from = "";
                            String to = "";
                            //上个月之前的数据存在时写入from，没有的话写入初回设置日
                            if (dataOfTheMonthBeforeLast.size() > 0) {
                                from = dateFormat.format(dataOfTheMonthBeforeLast.get(0).getCollectionTime());
                            } else {
                                from = dateFormat.format(ssrVm.getInitSetTime());
                            }
                            to = dateFormat.format(dataOfLastMonth.get(0).getCollectionTime());
                            if (StringUtils.isNotBlank(ssrVm.getTid()) && StringUtils.isNotBlank(ssrVm.getShopCode())) {
                                Map<String, Object> tidMap = new HashMap<>();
                                tidMap.put("tid", ssrVm.getTid());
                                tidMap.put("from", from);
                                tidMap.put("to", to);
                                dataList.add(tidMap);

                                if (!ssrVm.getTid().equalsIgnoreCase(ssrVm.getShopCode())) {
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("tid", ssrVm.getShopCode());
                                    map.put("from", from);
                                    map.put("to", to);
                                    dataList.add(map);
                                }
                            } else {
                                if (StringUtils.isNotBlank(ssrVm.getTid())) {
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("tid", ssrVm.getTid());
                                    map.put("from", from);
                                    map.put("to", to);
                                    dataList.add(map);
                                }
                                if (StringUtils.isNotBlank(ssrVm.getShopCode())) {
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("tid", ssrVm.getShopCode());
                                    map.put("from", from);
                                    map.put("to", to);
                                    dataList.add(map);
                                }
                            }
                        }
                    }
                }
            }

            StringBuilder sb = new StringBuilder();
            String sTitle = "TID/QRTID,FROM,TO,";
            String mapKey = "tid,from,to";
            File tempFile = null;
            FileOutputStream out = null;
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            String tempPath = "/opt/sanden_qr/pdfcovert/" + dtf.format(LocalDateTime.now()) + "_data" + ".csv";
            try  {
                //tempFile = File.createTempFile(dtf.format(LocalDateTime.now()) + "_data", ".csv");
                tempFile = new File(tempPath);
                if (!tempFile.createNewFile()) {
                    log.error("sftp创建临时文件失败");
                    return;
                }
                out = new FileOutputStream(tempFile);
                ExportUtil.doExport(dataList, sTitle, mapKey, out, sb.toString());

                //SFTP
                sftpGateway.uploadFile(tempFile);
            } catch (Exception e) {
                log.error("create file error", e);
            }finally {
                if ( out != null ) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        log.error("FileOutputStream close error", e);
                    }
                }

                if(null  != tempFile) {
                    tempFile.delete();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
