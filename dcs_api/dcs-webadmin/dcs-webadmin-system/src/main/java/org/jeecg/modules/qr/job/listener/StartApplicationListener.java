package org.jeecg.modules.qr.job.listener;

import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.qr.job.SsrDeleteOldFileJob;
import org.jeecg.modules.qr.job.SsrExportJob;
import org.jeecg.modules.qr.job.SsrVmGmoCooperationJob;
import org.jeecg.modules.qr.job.config.SchedulerConfig;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description: Timing task monitoring class
 * @Author: jeecg-boot
 * @Date:   2020-03-03
 * @Version: V0001
 */
@Component
@Slf4j
public class StartApplicationListener implements ApplicationListener<ContextRefreshedEvent> {
    @Autowired
    SchedulerConfig schedulerConfig;
    public static AtomicInteger count = new AtomicInteger(0);
    private static String TRIGGER_GROUP_NAME = "test_trriger";

    private static String TRIGGER_GROUP_NAME_EXPORT = "export_trriger";
    private static String JOB_GROUP_NAME = "test_job";
    private static String JOB_GROUP_NAME_EXPORT = "export_job";
    private static String GMO_GROUP_NAME = "test_gmo";
    private static String GMP_GROUP_NAME_SFTP = "gmo_sftp";
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 防止重复执行
        /*System.out.println("event.getApplicationContext().getParent() == null:" + (event.getApplicationContext().getParent() == null));
        System.out.println("parent:" + event.getApplicationContext().getParent());
        System.out.println("child:" + event.getApplicationContext());
        System.out.println("grandParent:" + (event.getApplicationContext().getParent() != null ? event.getApplicationContext().getParent().getParent() : null));
        System.out.println("count.incrementAndGet() <= 1:" + (count.incrementAndGet() <= 1));*/
//        if (event.getApplicationContext().getParent() != null && count.incrementAndGet() <= 1) {
//            initMyJob();
//        }
        /*if (count.incrementAndGet() <= 1) {
            initMyJob();
        }*/
        /*initMyJob();*/
    }

    /**
     * Initialize timed tasks
     */
    public void initMyJob() {
        Scheduler scheduler = null;
        try {
            scheduler = schedulerConfig.scheduler();

//            TriggerKey triggerKey = TriggerKey.triggerKey("triggerSsrDeleteOldFileJob", TRIGGER_GROUP_NAME);
//            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
//            if (null == trigger) {
//                Class clazz = SsrDeleteOldFileJob.class;
//                JobDetail jobDetail = JobBuilder.newJob(clazz).withIdentity("triggerSsrDeleteOldFileJob", JOB_GROUP_NAME).build();
//                //每天0点执行
////                CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0/2 * * * * ?");
//                CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 0 0 * * ?");
//                trigger = TriggerBuilder.newTrigger().withIdentity("triggerSsrDeleteOldFileJob", TRIGGER_GROUP_NAME)
//                        .withSchedule(scheduleBuilder).build();
//                scheduler.scheduleJob(jobDetail, trigger);
//                log.info("Quartz triggerSsrDeleteOldFileJob:...:{}", jobDetail.getKey());
//            } else {
//                log.info("triggerSsrDeleteOldFileJob已存在:{}", trigger.getKey());
//            }
//
//            TriggerKey triggerKey2 = TriggerKey.triggerKey("triggerDeadlineAlarm", TRIGGER_GROUP_NAME);
//            CronTrigger trigger2 = (CronTrigger) scheduler.getTrigger(triggerKey2);
//            if (null == trigger2) {
//                Class clazz = SsrDeadlineAlarmJob.class;
//                JobDetail jobDetail2 = JobBuilder.newJob(clazz).withIdentity("jobDeadlineAlarm", JOB_GROUP_NAME).build();
//                //每天16点执行
//                CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 0 16 * * ?");
//                trigger2 = TriggerBuilder.newTrigger().withIdentity("triggerDeadlineAlarm", TRIGGER_GROUP_NAME)
//                        .withSchedule(scheduleBuilder).build();
//                scheduler.scheduleJob(jobDetail2, trigger2);
//                log.info("Quartz 创建了jobDeadlineAlarm:...:{}", jobDetail2.getKey());
//            } else {
//                log.info("jobDeadlineAlarm已存在:{}", trigger2.getKey());
//            }
//
//            TriggerKey triggerKey3 = TriggerKey.triggerKey("triggerSendEmail", TRIGGER_GROUP_NAME);
//            CronTrigger trigger3 = (CronTrigger) scheduler.getTrigger(triggerKey3);
//            if (null == trigger3) {
//                Class clazz = SsrSendEmailJob.class;
//                JobDetail jobDetail3 = JobBuilder.newJob(clazz).withIdentity("jobSendEmail", JOB_GROUP_NAME).build();
//                //每隔15秒执行
//                CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0/15 * * * * ?");
//                trigger3 = TriggerBuilder.newTrigger().withIdentity("triggerSendEmail", TRIGGER_GROUP_NAME)
//                        .withSchedule(scheduleBuilder).build();
//                scheduler.scheduleJob(jobDetail3, trigger3);
//                log.info("Quartz 创建了jobSendEmail:...:{}", jobDetail3.getKey());
//            } else {
//                log.info("jobSendEmail已存在:{}", trigger3.getKey());
//            }


//            TriggerKey triggerKey4 = TriggerKey.triggerKey("triggerExportFile", TRIGGER_GROUP_NAME_EXPORT);
//            CronTrigger trigger4 = (CronTrigger) scheduler.getTrigger(triggerKey4);
//            if (null == trigger4) {
//                Class clazz = SsrExportJob.class;
//                JobDetail jobDetail4 = JobBuilder.newJob(clazz).withIdentity("jobExportFile", JOB_GROUP_NAME_EXPORT).build();
//                //每隔15秒执行
//                CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0/2 * * * * ?");
//                trigger4 = TriggerBuilder.newTrigger().withIdentity("triggerExportFile", TRIGGER_GROUP_NAME_EXPORT)
//                        .withSchedule(scheduleBuilder).build();
//                scheduler.scheduleJob(jobDetail4, trigger4);
//                log.info("Quartz 创建了jobExportFile:...:{}", jobDetail4.getKey());
//            } else {
//
//                log.info("jobExportFile已存在:{}", trigger4.getKey());
//            }

//            TriggerKey triggerKey5 = TriggerKey.triggerKey("triggerGmoCooperation", GMO_GROUP_NAME);
//            CronTrigger trigger5 = (CronTrigger) scheduler.getTrigger(triggerKey5);
//            if (null == trigger5) {
//                Class clazz = SsrVmGmoCooperationJob.class;
//                JobDetail jobDetail5 = JobBuilder.newJob(clazz).withIdentity("gmoSftp", GMP_GROUP_NAME_SFTP).build();
//                //每隔15秒执行
//                CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 0 6 1 * ? ");
//                trigger5 = TriggerBuilder.newTrigger().withIdentity("triggerGmoCooperation", GMO_GROUP_NAME)
//                        .withSchedule(scheduleBuilder).build();
//                scheduler.scheduleJob(jobDetail5, trigger5);
//                log.info("Quartz 创建了gmoSftp:...:{}", jobDetail5.getKey());
//            } else {
//                log.info("gmoSftp已存在:{}", trigger4.getKey());
//            }

            scheduler.start();
        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }
}
