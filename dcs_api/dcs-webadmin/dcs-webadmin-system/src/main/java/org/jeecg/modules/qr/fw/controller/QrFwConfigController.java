package org.jeecg.modules.qr.fw.controller;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.InstanceProfileCredentialsProvider;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.CopyObjectResult;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.qr.amazonOss.exception.BizException;
import org.jeecg.modules.qr.amazonOss.result.CodeMsg;
import org.jeecg.modules.qr.amazonOss.service.AmazonService;
import org.jeecg.modules.qr.fw.entity.QrFwConfig;
import org.jeecg.modules.qr.fw.service.IQrFwConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.qr.ssrVm.entity.SsrVm;
import org.jeecg.modules.qr.ssrVm.service.ISsrVmService;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: FW配信登録・履歴
 * @Author: jeecg-boot
 * @Date:   2020-11-18
 * @Version: V1.0
 */
@Api(tags="FW配信登録・履歴")
@RestController
@RequestMapping("/fw/qrFwConfig")
@Slf4j
@RequiresRoles(value = { "admin","ssradmin"}, logical = Logical.OR)
public class QrFwConfigController extends JeecgController<QrFwConfig, IQrFwConfigService> {
	 @Value(value = "${amazon.bucketName}")
	 private String bucketName;
	 @Value(value = "${amazon.url}")
	 private String url;

	@Autowired
	private AmazonService amazonService;

	@Autowired
	private IQrFwConfigService qrFwConfigService;
	@Autowired
	private ISsrVmService ssrVmService;

	 /**
	  * 批量FW配信
	  *fwUpdateBatch
	  * @param jsonObject QRデバイスid集合
	  * @return 操作结果
	  */
	 @PostMapping(value = "/fwUpdateBatch")
	 @RequiresPermissions("vm:add")
	 public Result<?> fwUpdateBatch(@RequestBody JSONObject jsonObject ) {

		 String ids = jsonObject.getString("ids");
		 String configDate = jsonObject.getString("configDate");
		 String terminalVersionSdna = jsonObject.getString("terminalVersionSdna");
		 String terminalVersionDrva = jsonObject.getString("terminalVersionDrva");
		 String terminalVersionSdnaUrl = jsonObject.getString("terminalVersionSdnaUrl");
		 String terminalVersionDrvaUrl = jsonObject.getString("terminalVersionDrvaUrl");

		 if(StringUtils.isEmpty(ids)){
			 return Result.error("Param error");
		 }
		 if(StringUtils.isEmpty(configDate)){
			 return Result.error("配信日時選んでください");
		 }
		 if(StringUtils.isEmpty(terminalVersionSdnaUrl) && StringUtils.isEmpty(terminalVersionDrvaUrl)){
			 return Result.error("ファイルを選択してください");
		 }

		 String sdna = "";
		 String drva = "";
		 //截取字符串
		 if(!StringUtils.isEmpty(terminalVersionSdna)){
			 int a = terminalVersionSdna.lastIndexOf("_");
			 int b = terminalVersionSdna.lastIndexOf(".");
			 sdna = terminalVersionSdna.substring(a+1, b);
		 }
		if(!StringUtils.isEmpty(terminalVersionDrva)){
			int a2 = terminalVersionDrva.lastIndexOf("_");
			int b2 = terminalVersionDrva.lastIndexOf(".");
			drva = terminalVersionDrva.substring(a2+1, b2);
		}

		String newSdnaUrl = "";
		String newDrvaUrl = "";
//		String pathUrl =url+"/";
		//把上传的文件复制到正式目录
		if(!StringUtils.isEmpty(terminalVersionSdnaUrl)){
			String sourceKey = terminalVersionSdnaUrl;
			String destinationKey = sourceKey.substring(14, sourceKey.length());

			try {
				CopyObjectResult copyObjectResult = amazonService.copyFile(new CopyObjectRequest(bucketName, sourceKey, bucketName, destinationKey));
				newSdnaUrl = destinationKey;
				//.withCannedAcl(CannedAccessControlList.PublicRead)
			} catch (AmazonServiceException e) {
				throw new BizException(CodeMsg.AMAZON_ERROR.fillArgs(e.getErrorMessage()));
			}
		}
		//把上传的文件复制到正式目录
		if(!StringUtils.isEmpty(terminalVersionDrvaUrl)){
			String sourceKey = terminalVersionDrvaUrl;
			String destinationKey = sourceKey.substring(14, sourceKey.length());

			try {
				CopyObjectResult copyObjectResult = amazonService.copyFile(new CopyObjectRequest(bucketName, sourceKey, bucketName, destinationKey));
				newDrvaUrl = destinationKey;
				//.withCannedAcl(CannedAccessControlList.PublicRead)
			} catch (AmazonServiceException e) {
				throw new BizException(CodeMsg.AMAZON_ERROR.fillArgs(e.getErrorMessage()));
			}
		}
		 LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 List<String> idList = Arrays.asList(ids.split(","));
//		 List<SsrFwUpdate> fwEntety = new ArrayList<>();

//		 SsrAppVersion newVersion = ssrAppVersionService.getById(versionId);
//		 if(null == newVersion){
//			 return Result.error("version error");
//		 }

		 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		 Date date = null;
		 try {
			 date = sdf.parse(configDate);

		 } catch (ParseException e) {
			 e.printStackTrace();
			 return Result.error("configDate error");
		 }

		 for (String id:idList) {
			 LambdaUpdateWrapper<SsrVm> updateWrapper = new UpdateWrapper().lambda();
			 updateWrapper.set(SsrVm::getConfigDate,configDate);
			 updateWrapper.set(SsrVm::getImplementDate,null);
			 updateWrapper.set(SsrVm::getTerminalVersionSdna,sdna);
			 updateWrapper.set(SsrVm::getTerminalVersionDrva,drva);
			 updateWrapper.set(SsrVm::getTerminalVersionDrvaUrl,newDrvaUrl);
			 updateWrapper.set(SsrVm::getTerminalVersionSdnaUrl,newSdnaUrl);
			 updateWrapper.eq(SsrVm::getId,id);
			 ssrVmService.update(updateWrapper);
		 }

		 return Result.OK("FW配信しました！");
	 }

	 /**
	  * FW解除（批量）
	  * @param ids
	  * @return
	  */
	 @GetMapping(value = "/fwDeteleBatch")
	 public Result<?> fwDeteleBatch(@RequestParam(name = "ids", required = true) String ids) {
		 if(StringUtils.isEmpty(ids)){
			 return Result.error("Param error");
		 }
		 List<String> idList = Arrays.asList(ids.split(","));
		 for (String id:idList) {
			 LambdaUpdateWrapper<SsrVm> updateWrapper = new UpdateWrapper().lambda();
			 updateWrapper.set(SsrVm::getConfigDate,null);
			 updateWrapper.set(SsrVm::getImplementDate,null);
			 updateWrapper.set(SsrVm::getTerminalVersionSdna,null);
			 updateWrapper.set(SsrVm::getTerminalVersionDrva,null);
			 updateWrapper.set(SsrVm::getTerminalVersionDrvaUrl,null);
			 updateWrapper.set(SsrVm::getTerminalVersionSdnaUrl,null);
			 updateWrapper.eq(SsrVm::getId,id);
			 ssrVmService.update(updateWrapper);
		 }

		 return Result.OK("配信を解除成功した");
	 }
}
