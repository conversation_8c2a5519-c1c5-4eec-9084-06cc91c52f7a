package org.jeecg.common.util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

public class ExportUtil {
	private final static Logger logger=LoggerFactory.getLogger(ExportUtil.class);
    /** CSV文件列分隔符 */
    private static final String CSV_COLUMN_SEPARATOR = ",";

    /** CSV文件列分隔符 */
    private static final String CSV_RN = "\r\n";

    /**
     *
     * @param dataList 集合数据
     * @param colNames 表头部数据
     * @param mapKey 查找的对应数据
     * @param os 返回结果
     */
    public static boolean doExport(List<Map<String, Object>> dataList, String colNames, String mapKey, OutputStream os,String head) {
        try {
            StringBuilder buf = new StringBuilder();
            buf.append(head);

            String[] colNamesArr = null;
            String[] mapKeyArr = null;

            colNamesArr = colNames.split(",");
            mapKeyArr = mapKey.split(",");

            // 完成数据csv文件的封装
            // 输出列头
            for (String aColNamesArr : colNamesArr) {
                buf.append(aColNamesArr).append(CSV_COLUMN_SEPARATOR);
            }
            buf.append(CSV_RN);

            if (null != dataList) {
                // 输出数据
                for (Map<String, Object> aDataList : dataList) {
                    for (String aMapKeyArr : mapKeyArr) {
                        buf.append(aDataList.get(aMapKeyArr)==null?"":signChange(aDataList.get(aMapKeyArr).toString())).append(CSV_COLUMN_SEPARATOR);
                    }
                    buf.append(CSV_RN);
                }
            }
            // 写出响应
            os.write(new byte[] { (byte)0xEF, (byte)0xBB, (byte)0xBF });
            os.write(buf.toString().getBytes(StandardCharsets.UTF_8));
            os.flush();
            return true;
        } catch (Exception e) {
        	logger.error("doExport错误...", e);
        }
        return false;
    }

    /**
     *
     * @param dataList 集合数据
     * @param colNames 表头部数据
     * @param mapKey 查找的对应数据
     * @param os 返回结果
     */
    public static boolean doExportCsvHeader( String colNames, String mapKey,OutputStream os,String head) {

        StringBuilder buf = new StringBuilder();
        try {

            buf.append(head);

            String[] colNamesArr = null;
            String[] mapKeyArr = null;

            colNamesArr = colNames.split(",");
            mapKeyArr = mapKey.split(",");

            // 完成数据csv文件的封装
            // 输出列头
            for (String aColNamesArr : colNamesArr) {
                buf.append(aColNamesArr).append(CSV_COLUMN_SEPARATOR);
            }
            buf.append(CSV_RN);

            os.write(new byte[] { (byte)0xEF, (byte)0xBB, (byte)0xBF });
            os.write(buf.toString().getBytes(StandardCharsets.UTF_8));
            os.flush();

            return true;
        } catch (Exception e) {
            logger.error("doExport错误...", e);
        }
        return false;
    }


    /**
     *
     * @param dataList 集合数据
     * @param colNames 表头部数据
     * @param mapKey 查找的对应数据
     * @param os 返回结果
     */
    public static boolean doExportCsvData(List<Map<String, Object>> dataList, String colNames,
                                       String mapKey, OutputStream os, Boolean isFinish) {
        StringBuilder buf = new StringBuilder();
        try {

            String[] colNamesArr = null;
            String[] mapKeyArr = null;

            colNamesArr = colNames.split(",");
            mapKeyArr = mapKey.split(",");


            if (null != dataList) {
                // 输出数据
                for (Map<String, Object> aDataList : dataList) {
                    for (String aMapKeyArr : mapKeyArr) {
                        buf.append(aDataList.get(aMapKeyArr)==null?"":signChange(aDataList.get(aMapKeyArr).toString())).append(CSV_COLUMN_SEPARATOR);
                    }
                    buf.append(CSV_RN);
                }
            }
//            if(isFinish){
//                // 写出响应
//                os.write(new byte[] { (byte)0xEF, (byte)0xBB, (byte)0xBF });
                os.write(buf.toString().getBytes(StandardCharsets.UTF_8));
                os.flush();
//            }

            return true;
        } catch (Exception e) {
            logger.error("doExport错误...", e);
        }
        return false;
    }

//
//    public static void exportExcel(String title, String fileName, String[] columns, String[] fields, List<?> list, String randomFolder) throws Exception {
//        //最大列宽数组
//        int[] columnWidthMax = new int[columns.length];
//
//        //新建工作薄
//        XSSFWorkbook workbook = new XSSFWorkbook();
//        XSSFSheet sheet = workbook.createSheet(title);
//
//        //标题行单元格字体
//        Font columnFont = workbook.createFont();
//        columnFont.setFontName("Courier New");
//        columnFont.setFontHeightInPoints((short) 12);
//        columnFont.setColor(IndexedColors.WHITE.index);
//
//        //标题行单元格样式
//        XSSFCellStyle columnStyle = workbook.createCellStyle();
//        columnStyle.setAlignment(HorizontalAlignment.CENTER);
//        columnStyle.setVerticalAlignment(VerticalAlignment.CENTER);
//        columnStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(128, 100, 162)));
//        columnStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//        columnStyle.setFont(columnFont);//设置字体
//
//        //数据集单元格样式-字体
//        Font dataFont = workbook.createFont();
//        dataFont.setFontName("Courier New");
//        dataFont.setFontHeightInPoints((short) 10);
//        dataFont.setColor(IndexedColors.BLACK.index);
//
//        //数据集单元格样式-奇
//        XSSFCellStyle dataStyle1 = workbook.createCellStyle();
//        dataStyle1.setAlignment(HorizontalAlignment.CENTER);
//        dataStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
//        dataStyle1.setFont(dataFont);
//
//        //数据集单元格样式-偶
//        XSSFCellStyle dataStyle2 = workbook.createCellStyle();
//        dataStyle2.setAlignment(HorizontalAlignment.CENTER);
//        dataStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
//        dataStyle2.setFillForegroundColor(new XSSFColor(new java.awt.Color(228, 223, 236)));
//        dataStyle2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//        dataStyle2.setFont(dataFont);
//
//        //写入标题行
//        XSSFRow rowColumn = sheet.createRow(0);
//        rowColumn.setHeightInPoints(30);
//        for (int i = 0; i < columns.length; i++) {
//            XSSFCell cellColum = rowColumn.createCell(i);
//            cellColum.setCellType(CellType.STRING);
//            cellColum.setCellValue(columns[i]);
//            cellColum.setCellStyle(columnStyle);
//            columnWidthMax[i] = columns[i].getBytes("GB2312").length;
//        }
//
//        //写入数据集
//        int index = 1;
//        XSSFRow rowData = null;
//        XSSFCell cellData = null;
//        for (Object obj : list) {
//            rowData = sheet.createRow(index);
//            rowData.setHeightInPoints(20);
//            JSONObject json = (JSONObject) JSONObject.toJSON(obj);
//            int fieldLength = fields.length;
//            for (int i = 0; i < fieldLength; i++) {
//                String data = json.getString(fields[i]);
//                if (!StringUtils.isBlank(data)) {
//                    int columnWidth = data.getBytes("GB2312").length;
//                    columnWidthMax[i] = Math.max(columnWidth, columnWidthMax[i]);
//                }
//                cellData = rowData.createCell(i);
//                cellData.setCellValue(data);
//                cellData.setCellStyle(index % 2 != 0 ? dataStyle1 : dataStyle2);
//            }
//            index++;
//        }
//
//        //调整列宽
//        for (int i = 0; i < columns.length; i++) {
//            sheet.setColumnWidth(i, (columnWidthMax[i] + 2) * 256);
//        }
//
//        OutputStream fileOutputStream = new FileOutputStream(TEMP_DIR + "/" + randomFolder + "/" + fileName + SUFFIX);
//        workbook.write(fileOutputStream);
//        fileOutputStream.flush();
//        fileOutputStream.close();
//        workbook.close();
//    }

    /**
    *
    * @param dataList 集合数据
    * @param colNames 表头部数据
    * @param mapKey 查找的对应数据
    * @param os 返回结果
    */
   public static boolean doExportExcel(List<Map<String, String>> dataList, String colNames, String mapKey,SXSSFWorkbook wb,Sheet sh,int currentIndex) {
       try {


           //标题行单元格字体
           Font columnFont = wb.createFont();
           columnFont.setFontName("Courier New");
           columnFont.setFontHeightInPoints((short) 12);
           columnFont.setColor(IndexedColors.WHITE.index);

           //标题行单元格样式
           XSSFCellStyle columnStyle = (XSSFCellStyle)wb.createCellStyle();
           columnStyle.setAlignment(HorizontalAlignment.CENTER);
           columnStyle.setVerticalAlignment(VerticalAlignment.CENTER);
           columnStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(128, 100, 162)));
           columnStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
           //设置字体
           columnStyle.setFont(columnFont);

           //数据集单元格样式-字体
           Font dataFont = wb.createFont();
           dataFont.setFontName("Courier New");
           dataFont.setFontHeightInPoints((short) 10);
           dataFont.setColor(IndexedColors.BLACK.index);

           //数据集单元格样式-奇
           XSSFCellStyle dataStyle1 = (XSSFCellStyle)wb.createCellStyle();
           dataStyle1.setAlignment(HorizontalAlignment.CENTER);
           dataStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
           dataStyle1.setFont(dataFont);

           //数据集单元格样式-偶
           XSSFCellStyle dataStyle2 = (XSSFCellStyle)wb.createCellStyle();
           dataStyle2.setAlignment(HorizontalAlignment.CENTER);
           dataStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
           dataStyle2.setFillForegroundColor(new XSSFColor(new java.awt.Color(228, 223, 236)));
           dataStyle2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
           dataStyle2.setFont(dataFont);



           String[] colNamesArr = null;
           String[] mapKeyArr = null;

           colNamesArr = colNames.split(",");
           mapKeyArr = mapKey.split(",");

           int[] columnWidthMax = new int[colNamesArr.length];

           Row currentRow = sh.createRow(currentIndex);
           currentRow.setHeightInPoints(30);
           int columnIndex = 0;
           // 完成数据csv文件的封装
           // 输出列头
           for (String aColNamesArr : colNamesArr) {
        	   Cell headerCell = currentRow.createCell(columnIndex);
               columnWidthMax[columnIndex] =aColNamesArr.getBytes(StandardCharsets.UTF_8).length;
				
        	   headerCell.setCellStyle(columnStyle);
        	   headerCell.setCellValue(aColNamesArr);
               columnIndex++;
           }

            // 输出数据
           if (null != dataList) {
               for (Map<String, String> aDataList : dataList) {
            	   Row dataRow = sh.createRow(++currentIndex);
                   dataRow.setHeightInPoints(20);
            	   columnIndex = 0;
                   for (String aMapKeyArr : mapKeyArr) {
                	   
//                	   if(aDataList.get(aMapKeyArr)!=null) {

                           Cell cellData = dataRow.createCell(columnIndex);
                           cellData.setCellValue(aDataList.get(aMapKeyArr));
                           cellData.setCellStyle(currentIndex % 2 != 0 ? dataStyle1 : dataStyle2);
                            if(null != aDataList.get(aMapKeyArr)){
                                int columnWidth = aDataList.get(aMapKeyArr).getBytes(StandardCharsets.UTF_8).length;
                                columnWidthMax[columnIndex] = Math.max(columnWidth, columnWidthMax[columnIndex]);
                            }
//                	   }
                	   columnIndex++;
                   }
               }
           }

            for (int i = 0; i < colNamesArr.length; i++) {
                sh.setColumnWidth(i, Math.min(3000, Math.min((columnWidthMax[i] + 2) * 256,10000)));
            }
          
       } catch (Exception e) {
       	logger.error("doExport错误...", e);
       }
       return false;
   }


    /**
     *
     * @param dataList 集合数据
     * @param colNames 表头部数据
     * @param mapKey 查找的对应数据
     * @param os 返回结果
     */
    public static boolean doExportExcelHeader(List<Map<String, String>> dataList, String colNames, String mapKey,SXSSFWorkbook wb,Sheet sh,int currentIndex) {
        try {


            //标题行单元格字体
            Font columnFont = wb.createFont();
            columnFont.setFontName("Courier New");
            columnFont.setFontHeightInPoints((short) 12);
            columnFont.setColor(IndexedColors.WHITE.index);

            //标题行单元格样式
            XSSFCellStyle columnStyle = (XSSFCellStyle)wb.createCellStyle();
            columnStyle.setAlignment(HorizontalAlignment.CENTER);
            columnStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            columnStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(128, 100, 162)));
            columnStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            //设置字体
            columnStyle.setFont(columnFont);

            //数据集单元格样式-字体
            Font dataFont = wb.createFont();
            dataFont.setFontName("Courier New");
            dataFont.setFontHeightInPoints((short) 10);
            dataFont.setColor(IndexedColors.BLACK.index);

            //数据集单元格样式-奇
            XSSFCellStyle dataStyle1 = (XSSFCellStyle)wb.createCellStyle();
            dataStyle1.setAlignment(HorizontalAlignment.CENTER);
            dataStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle1.setFont(dataFont);

            //数据集单元格样式-偶
            XSSFCellStyle dataStyle2 = (XSSFCellStyle)wb.createCellStyle();
            dataStyle2.setAlignment(HorizontalAlignment.CENTER);
            dataStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle2.setFillForegroundColor(new XSSFColor(new java.awt.Color(228, 223, 236)));
            dataStyle2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            dataStyle2.setFont(dataFont);



            String[] colNamesArr = null;
            String[] mapKeyArr = null;

            colNamesArr = colNames.split(",");
            mapKeyArr = mapKey.split(",");

            int[] columnWidthMax = new int[colNamesArr.length];

            Row currentRow = sh.createRow(currentIndex);
            currentRow.setHeightInPoints(30);
            int columnIndex = 0;
            // 完成数据csv文件的封装
            // 输出列头
            for (String aColNamesArr : colNamesArr) {
                Cell headerCell = currentRow.createCell(columnIndex);
                columnWidthMax[columnIndex] =aColNamesArr.getBytes(StandardCharsets.UTF_8).length;

                headerCell.setCellStyle(columnStyle);
                headerCell.setCellValue(aColNamesArr);
                columnIndex++;
            }

//            // 输出数据
//            if (null != dataList) {
//                for (Map<String, String> aDataList : dataList) {
//                    Row dataRow = sh.createRow(++currentIndex);
//                    dataRow.setHeightInPoints(20);
//                    columnIndex = 0;
//                    for (String aMapKeyArr : mapKeyArr) {
//
////                	   if(aDataList.get(aMapKeyArr)!=null) {
//
//                        Cell cellData = dataRow.createCell(columnIndex);
//                        cellData.setCellValue(aDataList.get(aMapKeyArr));
//                        cellData.setCellStyle(currentIndex % 2 != 0 ? dataStyle1 : dataStyle2);
//                        if(null != aDataList.get(aMapKeyArr)){
//                            int columnWidth = aDataList.get(aMapKeyArr).getBytes(StandardCharsets.UTF_8).length;
//                            columnWidthMax[columnIndex] = Math.max(columnWidth, columnWidthMax[columnIndex]);
//                        }
////                	   }
//                        columnIndex++;
//                    }
//                }
//            }

            for (int i = 0; i < colNamesArr.length; i++) {
                sh.setColumnWidth(i, Math.min(3000, Math.min((columnWidthMax[i] + 2) * 256,10000)));
            }

        } catch (Exception e) {
            logger.error("doExport错误...", e);
        }
        return false;
    }


    /**
     *
     * @param dataList 集合数据
     * @param colNames 表头部数据
     * @param mapKey 查找的对应数据
     * @param os 返回结果
     */
    public static boolean doExportExcelData(List<Map<String, String>> dataList, String colNames, String mapKey,SXSSFWorkbook wb,Sheet sh,int currentIndex) {
        try {


            //标题行单元格字体
            Font columnFont = wb.createFont();
            columnFont.setFontName("Courier New");
            columnFont.setFontHeightInPoints((short) 12);
            columnFont.setColor(IndexedColors.WHITE.index);

            //标题行单元格样式
            XSSFCellStyle columnStyle = (XSSFCellStyle)wb.createCellStyle();
            columnStyle.setAlignment(HorizontalAlignment.CENTER);
            columnStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            columnStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(128, 100, 162)));
            columnStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            //设置字体
            columnStyle.setFont(columnFont);

            //数据集单元格样式-字体
            Font dataFont = wb.createFont();
            dataFont.setFontName("Courier New");
            dataFont.setFontHeightInPoints((short) 10);
            dataFont.setColor(IndexedColors.BLACK.index);

            //数据集单元格样式-奇
            XSSFCellStyle dataStyle1 = (XSSFCellStyle)wb.createCellStyle();
            dataStyle1.setAlignment(HorizontalAlignment.CENTER);
            dataStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle1.setFont(dataFont);

            //数据集单元格样式-偶
            XSSFCellStyle dataStyle2 = (XSSFCellStyle)wb.createCellStyle();
            dataStyle2.setAlignment(HorizontalAlignment.CENTER);
            dataStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle2.setFillForegroundColor(new XSSFColor(new java.awt.Color(228, 223, 236)));
            dataStyle2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            dataStyle2.setFont(dataFont);



            String[] colNamesArr = null;
            String[] mapKeyArr = null;

            colNamesArr = colNames.split(",");
            mapKeyArr = mapKey.split(",");

            int[] columnWidthMax = new int[colNamesArr.length];
            int columnIndex = 0;
//            Row currentRow = sh.createRow(currentIndex);
//            currentRow.setHeightInPoints(30);
//
//            // 完成数据csv文件的封装
//            // 输出列头
//            for (String aColNamesArr : colNamesArr) {
//                Cell headerCell = currentRow.createCell(columnIndex);
//                columnWidthMax[columnIndex] =aColNamesArr.getBytes(StandardCharsets.UTF_8).length;
//
//                headerCell.setCellStyle(columnStyle);
//                headerCell.setCellValue(aColNamesArr);
//                columnIndex++;
//            }

            // 输出数据
            if (null != dataList) {
                for (Map<String, String> aDataList : dataList) {
                    Row dataRow = sh.createRow(++currentIndex);
                    dataRow.setHeightInPoints(20);
                    columnIndex = 0;
                    for (String aMapKeyArr : mapKeyArr) {

//                	   if(aDataList.get(aMapKeyArr)!=null) {

                        Cell cellData = dataRow.createCell(columnIndex);
                        cellData.setCellValue(aDataList.get(aMapKeyArr));
                        cellData.setCellStyle(currentIndex % 2 != 0 ? dataStyle1 : dataStyle2);
                        if(null != aDataList.get(aMapKeyArr)){
                            int columnWidth = aDataList.get(aMapKeyArr).getBytes(StandardCharsets.UTF_8).length;
                            columnWidthMax[columnIndex] = Math.max(columnWidth, columnWidthMax[columnIndex]);
                        }
//                	   }
                        columnIndex++;
                    }
                }
            }

            for (int i = 0; i < colNamesArr.length; i++) {
                sh.setColumnWidth(i, Math.min(3000, Math.min((columnWidthMax[i] + 2) * 256,10000)));
            }

        } catch (Exception e) {
            logger.error("doExport错误...", e);
        }
        return false;
    }

    /**
     * setHeader
     */
    public static void responseSetProperties(String fileName, HttpServletResponse response) throws UnsupportedEncodingException {
        // 设置文件后缀
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fn = fileName + sdf.format(new Date()) + ".csv";
        // 读取字符编码
        String utf = "UTF-8";

        // 设置响应
        response.setContentType("application/ms-txt.numberformat:@");
        response.setCharacterEncoding(utf);
        response.setHeader("Pragma", "public");
        response.setHeader("Cache-Control", "max-age=30");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fn, utf));
    }
    /**
     * 对csv文件的逗号进行转译
     * @param str
     * @return
     */
    private static String signChange(String str) {
        if(null == str){
            return "";
        }
        StringBuilder sb = new StringBuilder(str);

        if (str.contains("\"")) {
            int num = 0;
            for (int i = 0; i < str.length(); i++) {
                // 获取引号的总个数
                if (str.substring(i, (i + 1)).contains("\"")) {
                    num = num + 1;
                }
            }
            int[] a= new int[num];

            for (int j = 0; j < str.length(); j++) {
                // 获取引号的总个数
                if (str.substring(j, (j + 1)).contains("\"")) {
                    // 对引号的具体位置进行数组存储
                    num = num - 1;
                    a[num] = j;
                }
            }
            for (int value : a) {
                // 对每一个引号前加引号
                str = sb.insert(value, "\"").toString();
            }
            str=sb.insert(0, "\"").append("\"").toString();

        } else if (str.contains(",")) {
            str = sb.insert(0, "\"").append("\"").toString();
        }
        return str;
    }
}
