/**
 * 功能模块
 * @type {{LEAVE_EARLY: {text: string, value: string}, LATE: {text: string, value: string}, ABSENT: {text: string, value: string}, NO_SIGN: {text: string, value: string}, NORMAL: {text: string, value: string}}}
 */
export const FunctionEnum = {
  EoaCmsBanner: { path: 'modules/eoa/cmsoa/modules/EoaCmsBanner', formData: 'carouselImg' },
  EoaCmsNewsInfo: { path: 'modules/eoa/cmsoa/modules/EoaCmsNewsInfo', formData: 'newsInfo' },
  EoaCmsRuleInfo: { path: 'modules/eoa/cmsoa/modules/EoaCmsRuleInfo', formData: 'ruleDownInfo' },
  EoaCmsSignNews: { path: 'modules/eoa/cmsoa/modules/EoaCmsSignNews', formData: 'signNews' },
  EoaCmsUserNotice: { path: 'modules/eoa/cmsoa/modules/EoaCmsUserNotice', formData: 'userNotice' },
  EoaCmsPlan: { path: 'modules/eoa/cmsoa/modules/EoaCmsPlan', formData: '' },
  EoaCmsLink: { path: 'modules/eoa/cmsoa/modules/EoaCmsLink', formData: '' },
  EoaCmsCommUse:{ path: 'modules/eoa/cmsbpm/modules/EoaCmsCommUse', formData: '' },
  EoaCmsMyProcess:{ path: 'modules/eoa/cmsbpm/modules/EoaCmsMyProcess', formData: '' },
  EoaCmsApplyProcess:{ path: 'modules/eoa/cmsbpm/modules/EoaCmsApplyProcess', formData: '' },
  EoaCmsProcessNotice:{ path: 'modules/eoa/cmsbpm/modules/EoaCmsProcessNotice', formData: '' },
  EoaCmsProcessChatData:{ path: 'modules/eoa/cmsbpm/modules/EoaCmsProcessChatData', formData: '' },
  EoaCmsProcessTypeChat:{ path: 'modules/eoa/cmsbpm/modules/EoaCmsProcessTypeChat', formData: '' },
  EoaCmsEmail:{ path: 'modules/eoa/cmsbpm/modules/EoaCmsEmail', formData: '' },
}